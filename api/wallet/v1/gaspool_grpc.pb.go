// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/gaspool.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GasPoolSrv_ListGasPoolDepositToken_FullMethodName   = "/api.wallet.v1.GasPoolSrv/ListGasPoolDepositToken"
	GasPoolSrv_GetGasPoolBalance_FullMethodName         = "/api.wallet.v1.GasPoolSrv/GetGasPoolBalance"
	GasPoolSrv_GetGasPoolStat_FullMethodName            = "/api.wallet.v1.GasPoolSrv/GetGasPoolStat"
	GasPoolSrv_ListGasPoolConsumeRecord_FullMethodName  = "/api.wallet.v1.GasPoolSrv/ListGasPoolConsumeRecord"
	GasPoolSrv_ListGasPoolCashFlowRecord_FullMethodName = "/api.wallet.v1.GasPoolSrv/ListGasPoolCashFlowRecord"
	GasPoolSrv_GetPaymaster_FullMethodName              = "/api.wallet.v1.GasPoolSrv/GetPaymaster"
	GasPoolSrv_SendTx_FullMethodName                    = "/api.wallet.v1.GasPoolSrv/SendTx"
)

// GasPoolSrvClient is the client API for GasPoolSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GasPoolSrvClient interface {
	// 可充值币种列表
	ListGasPoolDepositToken(ctx context.Context, in *ListGasPoolDepositTokenReq, opts ...grpc.CallOption) (*ListGasPoolDepositTokenReply, error)
	// 查询GasPool余额
	GetGasPoolBalance(ctx context.Context, in *GetGasPoolBalanceReq, opts ...grpc.CallOption) (*GetGasPoolBalanceReply, error)
	// 查询GasPool统计数据
	GetGasPoolStat(ctx context.Context, in *GetGasPoolStatReq, opts ...grpc.CallOption) (*GetGasPoolStatReply, error)
	// GasPool消费记录列表
	ListGasPoolConsumeRecord(ctx context.Context, in *ListGasPoolConsumeRecordReq, opts ...grpc.CallOption) (*ListGasPoolConsumeRecordReply, error)
	// GasPool财务记录列表
	ListGasPoolCashFlowRecord(ctx context.Context, in *ListGasPoolCashFlowRecordReq, opts ...grpc.CallOption) (*ListGasPoolCashFlowRecordReply, error)
	// 获取paymaster address
	GetPaymaster(ctx context.Context, in *GetPaymasterReq, opts ...grpc.CallOption) (*GetPaymasterReply, error)
	// 发送交易
	SendTx(ctx context.Context, in *SendTxReq, opts ...grpc.CallOption) (*SendTxReply, error)
}

type gasPoolSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewGasPoolSrvClient(cc grpc.ClientConnInterface) GasPoolSrvClient {
	return &gasPoolSrvClient{cc}
}

func (c *gasPoolSrvClient) ListGasPoolDepositToken(ctx context.Context, in *ListGasPoolDepositTokenReq, opts ...grpc.CallOption) (*ListGasPoolDepositTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGasPoolDepositTokenReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_ListGasPoolDepositToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) GetGasPoolBalance(ctx context.Context, in *GetGasPoolBalanceReq, opts ...grpc.CallOption) (*GetGasPoolBalanceReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGasPoolBalanceReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_GetGasPoolBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) GetGasPoolStat(ctx context.Context, in *GetGasPoolStatReq, opts ...grpc.CallOption) (*GetGasPoolStatReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGasPoolStatReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_GetGasPoolStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) ListGasPoolConsumeRecord(ctx context.Context, in *ListGasPoolConsumeRecordReq, opts ...grpc.CallOption) (*ListGasPoolConsumeRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGasPoolConsumeRecordReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_ListGasPoolConsumeRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) ListGasPoolCashFlowRecord(ctx context.Context, in *ListGasPoolCashFlowRecordReq, opts ...grpc.CallOption) (*ListGasPoolCashFlowRecordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGasPoolCashFlowRecordReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_ListGasPoolCashFlowRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) GetPaymaster(ctx context.Context, in *GetPaymasterReq, opts ...grpc.CallOption) (*GetPaymasterReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymasterReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_GetPaymaster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gasPoolSrvClient) SendTx(ctx context.Context, in *SendTxReq, opts ...grpc.CallOption) (*SendTxReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendTxReply)
	err := c.cc.Invoke(ctx, GasPoolSrv_SendTx_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GasPoolSrvServer is the server API for GasPoolSrv service.
// All implementations must embed UnimplementedGasPoolSrvServer
// for forward compatibility.
type GasPoolSrvServer interface {
	// 可充值币种列表
	ListGasPoolDepositToken(context.Context, *ListGasPoolDepositTokenReq) (*ListGasPoolDepositTokenReply, error)
	// 查询GasPool余额
	GetGasPoolBalance(context.Context, *GetGasPoolBalanceReq) (*GetGasPoolBalanceReply, error)
	// 查询GasPool统计数据
	GetGasPoolStat(context.Context, *GetGasPoolStatReq) (*GetGasPoolStatReply, error)
	// GasPool消费记录列表
	ListGasPoolConsumeRecord(context.Context, *ListGasPoolConsumeRecordReq) (*ListGasPoolConsumeRecordReply, error)
	// GasPool财务记录列表
	ListGasPoolCashFlowRecord(context.Context, *ListGasPoolCashFlowRecordReq) (*ListGasPoolCashFlowRecordReply, error)
	// 获取paymaster address
	GetPaymaster(context.Context, *GetPaymasterReq) (*GetPaymasterReply, error)
	// 发送交易
	SendTx(context.Context, *SendTxReq) (*SendTxReply, error)
	mustEmbedUnimplementedGasPoolSrvServer()
}

// UnimplementedGasPoolSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGasPoolSrvServer struct{}

func (UnimplementedGasPoolSrvServer) ListGasPoolDepositToken(context.Context, *ListGasPoolDepositTokenReq) (*ListGasPoolDepositTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGasPoolDepositToken not implemented")
}
func (UnimplementedGasPoolSrvServer) GetGasPoolBalance(context.Context, *GetGasPoolBalanceReq) (*GetGasPoolBalanceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGasPoolBalance not implemented")
}
func (UnimplementedGasPoolSrvServer) GetGasPoolStat(context.Context, *GetGasPoolStatReq) (*GetGasPoolStatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGasPoolStat not implemented")
}
func (UnimplementedGasPoolSrvServer) ListGasPoolConsumeRecord(context.Context, *ListGasPoolConsumeRecordReq) (*ListGasPoolConsumeRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGasPoolConsumeRecord not implemented")
}
func (UnimplementedGasPoolSrvServer) ListGasPoolCashFlowRecord(context.Context, *ListGasPoolCashFlowRecordReq) (*ListGasPoolCashFlowRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGasPoolCashFlowRecord not implemented")
}
func (UnimplementedGasPoolSrvServer) GetPaymaster(context.Context, *GetPaymasterReq) (*GetPaymasterReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymaster not implemented")
}
func (UnimplementedGasPoolSrvServer) SendTx(context.Context, *SendTxReq) (*SendTxReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTx not implemented")
}
func (UnimplementedGasPoolSrvServer) mustEmbedUnimplementedGasPoolSrvServer() {}
func (UnimplementedGasPoolSrvServer) testEmbeddedByValue()                    {}

// UnsafeGasPoolSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GasPoolSrvServer will
// result in compilation errors.
type UnsafeGasPoolSrvServer interface {
	mustEmbedUnimplementedGasPoolSrvServer()
}

func RegisterGasPoolSrvServer(s grpc.ServiceRegistrar, srv GasPoolSrvServer) {
	// If the following call pancis, it indicates UnimplementedGasPoolSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GasPoolSrv_ServiceDesc, srv)
}

func _GasPoolSrv_ListGasPoolDepositToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGasPoolDepositTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).ListGasPoolDepositToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_ListGasPoolDepositToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).ListGasPoolDepositToken(ctx, req.(*ListGasPoolDepositTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_GetGasPoolBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGasPoolBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).GetGasPoolBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_GetGasPoolBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).GetGasPoolBalance(ctx, req.(*GetGasPoolBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_GetGasPoolStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGasPoolStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).GetGasPoolStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_GetGasPoolStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).GetGasPoolStat(ctx, req.(*GetGasPoolStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_ListGasPoolConsumeRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGasPoolConsumeRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).ListGasPoolConsumeRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_ListGasPoolConsumeRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).ListGasPoolConsumeRecord(ctx, req.(*ListGasPoolConsumeRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_ListGasPoolCashFlowRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGasPoolCashFlowRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).ListGasPoolCashFlowRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_ListGasPoolCashFlowRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).ListGasPoolCashFlowRecord(ctx, req.(*ListGasPoolCashFlowRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_GetPaymaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).GetPaymaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_GetPaymaster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).GetPaymaster(ctx, req.(*GetPaymasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GasPoolSrv_SendTx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendTxReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GasPoolSrvServer).SendTx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GasPoolSrv_SendTx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GasPoolSrvServer).SendTx(ctx, req.(*SendTxReq))
	}
	return interceptor(ctx, in, info, handler)
}

// GasPoolSrv_ServiceDesc is the grpc.ServiceDesc for GasPoolSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GasPoolSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.GasPoolSrv",
	HandlerType: (*GasPoolSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListGasPoolDepositToken",
			Handler:    _GasPoolSrv_ListGasPoolDepositToken_Handler,
		},
		{
			MethodName: "GetGasPoolBalance",
			Handler:    _GasPoolSrv_GetGasPoolBalance_Handler,
		},
		{
			MethodName: "GetGasPoolStat",
			Handler:    _GasPoolSrv_GetGasPoolStat_Handler,
		},
		{
			MethodName: "ListGasPoolConsumeRecord",
			Handler:    _GasPoolSrv_ListGasPoolConsumeRecord_Handler,
		},
		{
			MethodName: "ListGasPoolCashFlowRecord",
			Handler:    _GasPoolSrv_ListGasPoolCashFlowRecord_Handler,
		},
		{
			MethodName: "GetPaymaster",
			Handler:    _GasPoolSrv_GetPaymaster_Handler,
		},
		{
			MethodName: "SendTx",
			Handler:    _GasPoolSrv_SendTx_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/gaspool.proto",
}
