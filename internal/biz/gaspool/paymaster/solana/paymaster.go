package solana

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	solanaCom "byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/model"
	"context"
	"fmt"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

// paymaster.go - Solana paymaster 主文件
// 包含 paymaster 结构体定义、构造函数和核心接口实现
// 支持 Solana 链的交易处理、签名验证、费用估算等功能

// Solana paymaster 相关常量
const (
	DefaultPriceExpireSeconds = 60 // 默认价格过期时间（秒）
)

var decimalsUSDT = decimal.NewFromInt(1000000)

type Paymaster struct {
	log              *log.Helper
	tokenPriceReader base.TokenPriceReader
	hotAccountReader base.HotAccountReader
	solCli           *solanaCom.SmartNodeSelectionClient
	gpMgr            base.GasPoolMgr
	// 缓存字段，避免重复计算
	cachedPrivateKey *solana.PrivateKey
	cachedPublicKey  *solana.PublicKey

	// 配置字段
	priceExpireSeconds int64 // 价格过期时间（秒）
}

// NewPaymaster 创建新的Solana paymaster实例
func NewPaymaster(logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	hotAccountReader base.HotAccountReader,
	solCli *solanaCom.SmartNodeSelectionClient,
	gpMgr base.GasPoolMgr,
) *Paymaster {
	return &Paymaster{
		log:                log.NewHelper(logger),
		tokenPriceReader:   tokenPriceReader,
		hotAccountReader:   hotAccountReader,
		solCli:             solCli,
		gpMgr:              gpMgr,
		priceExpireSeconds: DefaultPriceExpireSeconds,
	}
}

// SetPriceExpireSeconds 设置价格过期时间（秒）
func (pm *Paymaster) SetPriceExpireSeconds(seconds int64) {
	if seconds > 0 {
		pm.priceExpireSeconds = seconds
		pm.log.Infof("价格过期时间已更新为: %d 秒", seconds)
	}
}

// DecodeUserTx 解码用户交易数据
func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (tx *gaspool.UserTx, err error) {
	// 使用统一的验证函数
	if err := validateRawTx(rawTxHex); err != nil {
		return nil, err
	}

	tx, err = pm.decodeTransferTx(ctx, rawTxHex)
	if err != nil {
		return nil, wrapError("decodeTransferTx", err)
	}

	tx.TxType = txType
	tx.RawTxHex = rawTxHex

	tx.ChainIndex = constant.SolChainIndex
	return tx, nil
}

// VerifyUserTxSignature 验证用户交易签名
func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	return pm.verifyTxSignature(tx.RawTxHex)
}

// EstimateGas 估算交易gas费用
func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	// 使用统一的验证函数
	if err := validateUserTx(tx); err != nil {
		return nil, err
	}

	// 获取交易费用（以lamports为单位）
	fee, err := pm.GetTransactionFeeWithContext(ctx, tx.RawTxHex)
	if err != nil {
		return nil, wrapError("get transaction fee", err)
	}

	// 准备交易对象以便分析租金费用
	transaction, err := pm.prepareTransaction(tx.RawTxHex)
	if err != nil {
		return nil, wrapError("prepareTransaction", err)
	}

	// 计算租金费用（Solana 特有的账户租金机制）
	// Solana 要求新创建的账户必须有足够的 SOL 余额来支付租金，或者达到免租金状态
	rentFee, err := pm.CalculateRentFeeForTransaction(ctx, transaction)
	if err != nil {
		pm.log.Warnf("计算租金费用失败: %v，将使用零租金费用", err)
		rentFee = decimal.Zero
	}

	// 总费用 = 交易费用 + 租金费用
	totalFee := fee.Add(rentFee)

	pm.log.Debugf("费用明细 - 交易费用: %s lamports, 租金费用: %s lamports, 总费用: %s lamports",
		fee.String(), rentFee.String(), totalFee.String())

	// 获取SOL价格
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, constant.SolChainIndex, "")
	if err != nil {
		return nil, wrapError("get SOL price", err)
	}
	if pm.isPriceTimeExpired(timeUnix) {
		return nil, fmt.Errorf("sol price expired: %d", timeUnix)
	}

	// 计算USDT费用（基于总费用，包含交易费用和租金费用）
	gasUSDT := totalFee.Div(constant.BaseUnitPerSOL).Mul(price).Mul(decimalsUSDT)

	gas := &gaspool.UserTxGas{
		Gas:           totalFee, // 使用包含租金费用的总费用
		GasUSDT:       gasUSDT,
		Price:         price,
		PriceTimeUnix: timeUnix,
	}

	pm.log.Infof("Solana 交易费用估算完成 - 总费用: %s lamports (%.6f SOL), USDT 价值: %s",
		totalFee.String(),
		totalFee.Div(constant.BaseUnitPerSOL).InexactFloat64(),
		gasUSDT.String())

	return gas, nil
}

func (pm *Paymaster) AuditUserTx(ctx context.Context, tx *gaspool.UserTx, gas *gaspool.UserTxGas) (ok bool, err error) {
	return true, nil
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, tx *gaspool.UserTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("Solana交易处理完成，耗时: %v", duration)
	}()

	// 使用统一的验证函数
	if err = validateUserTx(tx); err != nil {
		if tx != nil {
			pm.log.Errorf("验证失败: %v", err)
		}
		return
	}

	// 准备交易
	transaction, err := pm.prepareTransaction(tx.RawTxHex)
	if err != nil {
		err = wrapErrorWithID("prepareTransaction", int64(tx.UserID), err)
		return
	}

	// 发送交易到Solana网络
	sig, err := pm.solCli.Select().SendTransactionWithOpts(
		ctx,
		transaction,
		rpc.TransactionOpts{
			SkipPreflight:       false,
			PreflightCommitment: rpc.CommitmentFinalized,
		},
	)
	if err != nil {
		err = fmt.Errorf("solana send err: %v", err)
		return
	}

	tx.TxHash = sig.String()

	// deposit
	_, err = pm.gpMgr.DepositGasPool(ctx, tx.UserID, tx.ValueUSDT, tx.ChainIndex, tx.TxHash)
	if err != nil {
		pm.log.Errorf("DepositGasPool by sendRawTxByDepositWithoutGas: %v: userID=%d, valueUSDT=%s, chainIndex=%d, txHash=%s",
			err, tx.UserID, tx.ValueUSDT, tx.ChainIndex, tx.TxHash)
	}
	return
}

// SendSponsorTx 发送原始交易到Solana网络
func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	// 性能监控：记录开始时间
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		pm.log.Infof("Solana交易处理完成，耗时: %v", duration)
	}()

	// 使用统一的验证函数
	if err = validateSponsorTx(tx); err != nil {
		if tx != nil {
			pm.log.Errorf("交易ID %d 验证失败: %v", tx.ID, err)
		}
		return
	}

	// 准备交易
	transaction, err := pm.prepareTransaction(tx.RawTxHex)
	if err != nil {
		err = wrapErrorWithID("prepareTransaction", int64(tx.ID), err)
		return
	}

	// 为交易添加paymaster和用户签名
	if err = pm.signTransaction(transaction); err != nil {
		err = wrapErrorWithID("signTransaction", int64(tx.ID), err)
		return
	}

	// 发送交易到Solana网络
	sig, err := pm.solCli.Select().SendTransactionWithOpts(
		ctx,
		transaction,
		rpc.TransactionOpts{
			SkipPreflight:       false,
			PreflightCommitment: rpc.CommitmentFinalized,
		},
	)
	if err != nil {
		pm.log.Errorf("发送Solana交易失败，交易ID: %d, 错误: %v", tx.ID, err)
		err = fmt.Errorf("solana send err: %v", err)
		return
	}

	tx.TxHash = sig.String()
	pm.log.Infof("Solana交易发送成功，交易ID: %d, 交易哈希: %s", tx.ID, sig.String())
	return nil
}
