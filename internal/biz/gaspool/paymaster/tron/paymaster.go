package tron

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/model"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	tronChain "byd_wallet/internal/biz/syncer/chain/tron"

	"github.com/alitto/pond/v2"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"
)

type WaitGasTask struct {
	TxID     uint
	TimeUnix int64
}

type WaitDepositTxTask struct {
	TxID     uint
	TimeUnix int64
}

type AsyncTaskMgr interface {
	AddWaitGasTask(ctx context.Context, task *WaitGasTask) error
	AllWaitGasTask(ctx context.Context) ([]*WaitGasTask, error)
	RemoveWaitGasTask(ctx context.Context, txID uint) error

	// AddWaitDepositTxTask(ctx context.Context, task *WaitDepositTxTask) error
	// AllWaitDepositTxTask(ctx context.Context) ([]*WaitDepositTxTask, error)
	// RemoveWaitDepositTxTask(ctx context.Context, txID uint) error
}

type BuyBandwidthReq struct {
	ReceiveAddr string
	PledgeNum   decimal.Decimal
	PledgeHour  int
	ActivateFee decimal.Decimal
}

type BuyBandwidthReply struct {
	OrderID string
	Price   decimal.Decimal
	Amount  decimal.Decimal
}

type BuyEnergyReq struct {
	ReceiveAddr string
	PledgeNum   decimal.Decimal
	PledgeHour  int
}

type BuyEnergyReply struct {
	OrderID string
	Amount  decimal.Decimal
	Price   decimal.Decimal
}

type GetEnergyPriceReq struct {
	PledgeNum  decimal.Decimal
	PledgeHour int
}

type TransferTrxReq struct {
	ReceiveAddress string
	Amount         decimal.Decimal
}

type TransferTrxReply struct {
	TxHash string
	Amount decimal.Decimal
}

type TronBandwidthPayWallet interface {
	TransferTrx(ctx context.Context, req *TransferTrxReq) (reply *TransferTrxReply, err error)
}

type TronRentApi interface {
	GetEnergyPrice(ctx context.Context, req *GetEnergyPriceReq) (price decimal.Decimal, err error)
	BuyEnergy(ctx context.Context, req *BuyEnergyReq) (reply *BuyEnergyReply, err error)
}

type TronAddressChecker interface {
	IsActivatedAddress(ctx context.Context, address string) (bool, error)
}

type Paymaster struct {
	log *log.Helper

	tokenPriceReader       base.TokenPriceReader
	hotAccountReader       base.HotAccountReader
	stxMgr                 base.GasPoolSponsorTxMgr
	gpMgr                  base.GasPoolMgr
	tronCli                *tronChain.RoundRobinClient
	tronBandwidthPayWallet TronBandwidthPayWallet
	tronRentApi            TronRentApi
	tronRentHour           int
	tronAddressChecker     TronAddressChecker
	asyncTaskMgr           AsyncTaskMgr

	// 缓存字段，避免重复计算
	cachedAddress string

	stopCh                chan struct{}
	waitGasTaskPool       pond.Pool
	WaitDepositTxTaskPool pond.Pool
}

func NewPaymaster(logger log.Logger,
	tokenPriceReader base.TokenPriceReader,
	hotAccountReader base.HotAccountReader,
	stxMgr base.GasPoolSponsorTxMgr,
	gpMgr base.GasPoolMgr,
	tronCli *tronChain.RoundRobinClient,
	tronBandwidthPayWallet TronBandwidthPayWallet,
	tronRentApi TronRentApi,
	tronAddressChecker TronAddressChecker,
	asyncTaskMgr AsyncTaskMgr,
) *Paymaster {
	return &Paymaster{
		log: log.NewHelper(logger),

		tokenPriceReader:       tokenPriceReader,
		hotAccountReader:       hotAccountReader,
		stxMgr:                 stxMgr,
		gpMgr:                  gpMgr,
		tronCli:                tronCli,
		tronBandwidthPayWallet: tronBandwidthPayWallet,
		tronRentApi:            tronRentApi,
		tronRentHour:           1, // default: 1h
		tronAddressChecker:     tronAddressChecker,
		asyncTaskMgr:           asyncTaskMgr,
	}
}

func (pm *Paymaster) isPriceTimeExpired(timeUnix int64) bool {
	return time.Now().Unix() > timeUnix+60 // TODO: config ???
}

func (pm *Paymaster) DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*gaspool.UserTx, error) {
	// transaction
	rawTxBytes, err := hex.DecodeString(rawTxHex)
	if err != nil {
		return nil, fmt.Errorf("decode raw tx hex: %v", err)
	}
	var tx core.Transaction
	err = proto.Unmarshal(rawTxBytes, &tx)
	if err != nil {
		return nil, fmt.Errorf("unmarshal tx: %v", err)
	}
	if tx.RawData == nil {
		return nil, errors.New("tx raw data is nil")
	}

	// expiration // TODO: config???
	if tx.RawData.Expiration < time.Now().Add(5*time.Minute).UnixMilli() {
		return nil, errors.New("tx expiration is too short")
	}

	userTx := &gaspool.UserTx{}
	userTx.BroadcastTx = &tx

	// transaction raw
	txRaw := tx.RawData
	userTx.FeeLimit = decimal.NewFromInt(txRaw.FeeLimit)

	// tx hash
	txRawBts, err := proto.Marshal(txRaw)
	if err != nil {
		return nil, fmt.Errorf("marshal tx raw: %w", err)
	}
	hash := sha256.Sum256(txRawBts)
	userTx.TxHashBytes = hash[:]
	userTx.TxHash = hex.EncodeToString(userTx.TxHashBytes)

	// contract
	if len(txRaw.Contract) != 0 {
		return nil, errors.New("raw data contract is empty")
	}
	contract := txRaw.Contract[0]
	switch contract.Type {
	case core.Transaction_Contract_TransferContract:
		var data core.TransferContract
		if err = contract.GetParameter().UnmarshalTo(&data); err != nil {
			return nil, fmt.Errorf("unmarshal transfer contract: %v", err)
		}
		userTx.From = bytes2AddressString(data.GetOwnerAddress())
		userTx.To = bytes2AddressString(data.GetToAddress())
		userTx.Value = decimal.NewFromInt(data.GetAmount())
	case core.Transaction_Contract_TriggerSmartContract:
		var data core.TriggerSmartContract
		if err = contract.GetParameter().UnmarshalTo(&data); err != nil {
			return nil, fmt.Errorf("unmarshal trigger smart contract: %v", err)
		}
		userTx.From = bytes2AddressString(data.GetOwnerAddress())
		userTx.Contract = bytes2AddressString(data.GetContractAddress())
		methodSig := decodeTRC20MethodSignature(data.GetData())
		if methodSig != trc20TransferMethodSignature {
			return nil, fmt.Errorf("unknown method signature: %s", methodSig)
		}
		toAddr, value, err := decodeTRC20TransferData(data.GetData())
		if err != nil {
			return nil, fmt.Errorf("decode trc20 transfer data error: %v", err)
		}
		userTx.To = toAddr
		userTx.Value = decimal.NewFromBigInt(value, 0)
	default:
		return nil, fmt.Errorf("unknown contract type: %s", contract.Type)
	}

	userTx.TxType = txType
	userTx.RawTxHex = rawTxHex
	userTx.ChainIndex = constant.TronChainIndex
	return userTx, nil
}

func (pm *Paymaster) VerifyUserTxSignature(ctx context.Context, tx *gaspool.UserTx) (bool, error) {
	if tx.BroadcastTx == nil {
		return false, errors.New("broadcast tx is nil")
	}
	btx, ok := tx.BroadcastTx.(*core.Transaction)
	if !ok {
		return false, errors.New("invalid broadcast tx")
	}
	if len(btx.Signature) == 0 {
		return false, errors.New("signature is empty")
	}
	return verifyTxSignature(tx.TxHashBytes, btx.Signature[0], tx.From)
}

func (pm *Paymaster) EstimateGas(ctx context.Context, tx *gaspool.UserTx) (*gaspool.UserTxGas, error) {
	switch tx.TxType {
	case model.GasPoolTxTypeTransfer,
		model.GasPoolTxTypeDeposit,
		model.GasPoolTxTypeDepositUseGas,
		model.GasPoolTxTypeDepositPreReduceGas:
		return pm.estimateTransferGas(ctx, tx)
	default:
		return nil, fmt.Errorf("unknown tx type: %s", tx.TxType)
	}
}

func (pm *Paymaster) estimateTransferGas(ctx context.Context, tx *gaspool.UserTx) (gas *gaspool.UserTxGas, err error) {
	// chain params
	cli := pm.tronCli.Next()
	params, err := cli.Client.GetChainParameters(ctx, &api.EmptyMessage{})
	if err != nil {
		err = fmt.Errorf("GetChainParameters: %w", err)
		return
	}
	bandwidthPrice := decimal.Zero
	chainActivateFee := decimal.Zero
	f1, f2 := false, false
	for _, param := range params.GetChainParameter() {
		if f1 && f2 {
			break
		}
		if param.Key == "getTransactionFee" {
			bandwidthPrice = decimal.NewFromInt(param.Value)
			f1 = true
			continue
		}
		if param.Key == "getCreateNewAccountFeeInSystemContract" {
			chainActivateFee = decimal.NewFromInt(param.Value)
			f2 = true
			continue
		}
	}
	if bandwidthPrice.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("invalid bandwidth price")
	}
	if chainActivateFee.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("invalid chain activate fee")
	}

	// activate fee
	activateFee := decimal.Zero
	if tx.Contract == "" { // TRX: check to_address
		ok, err := pm.tronAddressChecker.IsActivatedAddress(ctx, tx.To)
		if err != nil {
			return nil, fmt.Errorf("check activate status: %w", err)
		}
		if !ok {
			activateFee = chainActivateFee
		}
	} else { // TRC20: check from_address
		ok, err := pm.tronAddressChecker.IsActivatedAddress(ctx, tx.From)
		if err != nil {
			return nil, fmt.Errorf("check activate status: %w", err)
		}
		if !ok {
			activateFee = chainActivateFee
		}
	}
	if activateFee.IsNegative() {
		return nil, errors.New("invalid activate fee")
	}

	// bandwidth // RawTxBytes = proto.Marshal(core.Transaction{RawData: txRaw, Signature: [][]byte{sig}})
	bandwidthCost := decimal.NewFromInt(int64(len(tx.RawTxBytes) + 64))

	// energy
	energyCost := decimal.Zero
	if tx.Contract != "" {
		triggerData := fmt.Sprintf(`[{"address":"%s"},{"uint256":"%s"}]`, tx.To, tx.Value.String())
		res, terr := cli.EstimateEnergy(tx.From, tx.Contract, "transfer(address,uint256)", triggerData, 0, "", 0)
		if terr != nil {
			err = fmt.Errorf("EstimateEnergy: %w", terr)
			return
		}
		energyCost = decimal.NewFromInt(res.EnergyRequired)
	}

	// energy price
	energyPrice, err := pm.tronRentApi.GetEnergyPrice(ctx, &GetEnergyPriceReq{
		PledgeNum:  energyCost,
		PledgeHour: pm.tronRentHour,
	})
	if err != nil {
		err = fmt.Errorf("GetEnergyPrice: %w", err)
		return
	}
	if energyPrice.LessThanOrEqual(decimal.Zero) {
		return nil, errors.New("invalid energy price")
	}

	// trx spot price
	price, timeUnix, err := pm.tokenPriceReader.GetTokenLatestPriceUSDT(ctx, constant.TronChainIndex, "")
	if err != nil {
		err = fmt.Errorf("GetTokenLatestPriceUSDT: %w", err)
		return
	}
	if pm.isPriceTimeExpired(timeUnix) {
		err = fmt.Errorf("token price expired: %d", timeUnix)
		return
	}
	if price.LessThanOrEqual(decimal.Zero) {
		err = errors.New("invalid trx price")
		return
	}

	// total cost
	gasTrxSun := bandwidthCost.Mul(bandwidthPrice).Add(energyCost.Mul(energyPrice)).Add(activateFee)
	gasUSDT := gasTrxSun.Div(decimalsTRX).Mul(price).Mul(decimalsUSDT)

	gas = &gaspool.UserTxGas{
		Gas:            gasTrxSun,
		GasUSDT:        gasUSDT,
		Price:          price,
		PriceTimeUnix:  timeUnix,
		Bandwidth:      bandwidthCost,
		BandwidthPrice: bandwidthPrice,
		Energy:         energyCost,
		EnergyPrice:    energyPrice,
		ActivateFee:    activateFee,
	}
	return
}

func (pm *Paymaster) AuditUserTx(ctx context.Context, tx *gaspool.UserTx, gas *gaspool.UserTxGas) (ok bool, err error) {
	if tx.TxType.IsUseGasPool() {
		bandwidthBalance, energyBalance, terr := getAccountAvailableFee(pm.tronCli.Next(), tx.From)
		if terr != nil {
			err = fmt.Errorf("get account available fee: %w", terr)
			return
		}
		if gas.Bandwidth.LessThanOrEqual(bandwidthBalance) &&
			gas.Energy.LessThanOrEqual(energyBalance) {
			ok = false
			return
		}
	}
	ok = true
	return
}

func (pm *Paymaster) updateTxFailStatusWithoutFee(ctx context.Context, id uint, failErr error) error {
	update := &model.GasPoolSponsorTx{}
	update.ID = id
	update.Status = model.GasPoolTxStatusFail
	update.Reason = failErr.Error()
	return pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
}

func (pm *Paymaster) updateTxFailStatusWithFee(ctx context.Context,
	tx *model.GasPoolSponsorTx,
	update *model.GasPoolSponsorTx,
	failErr error,
	usedTrx decimal.Decimal) error {
	var refundFlowID uint
	if tx.ReduceFlowID > 0 {
		usedUSDT := usedTrx.Div(decimalsTRX).Mul(tx.Price).Mul(decimalsUSDT)
		refundAmount := tx.GasUSDT.Sub(usedUSDT)
		if refundAmount.IsPositive() {
			flow, err := pm.gpMgr.RefundGasPool(ctx, tx.ReduceFlowID, refundAmount)
			if err != nil {
				pm.log.Errorf("refund gas pool err: %v: reduceFlowID=%d, refundAmount=%s", err, tx.ReduceFlowID, refundAmount.String())
			} else {
				refundFlowID = flow.ID
			}
		}
	}
	if update == nil {
		update = &model.GasPoolSponsorTx{}
	}
	update.ID = tx.ID
	update.Status = model.GasPoolTxStatusFail
	update.Reason = failErr.Error()
	update.RefundFlowID = refundFlowID
	return pm.stxMgr.UpdateGasPoolSponsorTx(ctx, tx)
}

func (pm *Paymaster) SendDepositTx(ctx context.Context, userTx *gaspool.UserTx) (err error) {
	if userTx.BroadcastTx == nil {
		err = errors.New("broadcast tx is nil")
		return
	}
	tx, ok := userTx.BroadcastTx.(*core.Transaction)
	if !ok {
		err = errors.New("invalid broadcast tx")
		return
	}
	cli := pm.tronCli.Next()
	bResp, err := cli.Broadcast(tx)
	if err != nil {
		err = fmt.Errorf("broadcast tx error: %w", err)
		return
	}

	// broadcast fail
	if !bResp.Result {
		err = fmt.Errorf("broadcast tx fail: %s", bResp.String())
		return
	}

	// deposit
	_, err = pm.gpMgr.DepositGasPool(ctx, userTx.UserID, userTx.ValueUSDT, userTx.ChainIndex, userTx.TxHash)
	if err != nil {
		pm.log.Errorf("DepositGasPool by sendRawTxByDepositWithoutGas: %v: userID=%d, valueUSDT=%s, chainIndex=%d, txHash=%s",
			err, userTx.UserID, userTx.ValueUSDT, userTx.ChainIndex, userTx.TxHash)
	}
	return
}

func (pm *Paymaster) SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (err error) {
	bandwidthBalance, energyBalance, err := getAccountAvailableFee(pm.tronCli.Next(), tx.From)
	if err != nil {
		err = fmt.Errorf("get account available fee error: %w", err)
		if updateErr := pm.updateTxFailStatusWithoutFee(ctx, tx.ID, err); updateErr != nil {
			pm.log.Errorf("update tx fail status without fee error: %v: txID=%d, reason=%v",
				updateErr, tx.ID, err)
		}
		return
	}
	update := &model.GasPoolSponsorTx{}
	update.ID = tx.ID

	needBandwidth := bandwidthBalance.LessThan(tx.Bandwidth)
	if needBandwidth {
		reply, terr := pm.tronBandwidthPayWallet.TransferTrx(ctx, &TransferTrxReq{
			Amount:         tx.Bandwidth.Mul(tx.BandwidthPrice).Add(tx.ActivateFee),
			ReceiveAddress: tx.From,
		})
		if terr != nil {
			err = fmt.Errorf("buy bandwidth error: %w", terr)
			return
		}
		update.NativeTxHash = reply.TxHash
		update.NativeTxFee = reply.Amount
	}
	needEnergy := energyBalance.LessThan(tx.Energy)
	if needEnergy {
		reply, terr := pm.tronRentApi.BuyEnergy(ctx, &BuyEnergyReq{
			PledgeHour:  pm.tronRentHour,
			PledgeNum:   tx.Energy,
			ReceiveAddr: tx.From,
		})
		if terr != nil {
			err = fmt.Errorf("buy energy error: %w", terr)
			if updateErr := pm.updateTxFailStatusWithFee(ctx, tx, update, err, update.NativeTxFee); updateErr != nil {
				pm.log.Errorf("update tx fail status with fee error: %v: txID=%d, reason=%v",
					updateErr, tx.ID, err)
			}
			return
		}
		update.RentEnergyOrderID = reply.OrderID
		update.RentEnergyFee = reply.Amount
		update.ActualEnergyPrice = reply.Price
	}

	if !needBandwidth && !needEnergy {
		err = errors.New("user tx not allowed")
		if updateErr := pm.updateTxFailStatusWithoutFee(ctx, tx.ID, err); updateErr != nil {
			pm.log.Errorf("update tx fail status without fee error: %v: txID=%d, reason=%v",
				updateErr, tx.ID, err)
		}
	}

	task := &WaitGasTask{
		TxID:     tx.ID,
		TimeUnix: time.Now().Unix(),
	}
	if err = pm.asyncTaskMgr.AddWaitGasTask(ctx, task); err != nil {
		pm.log.Errorf("AddWaitGasTask: %v: task=%+v", err, task)
	}
	update.Status = model.GasPoolTxStatusWaitGas
	if terr := pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); terr != nil {
		pm.log.Errorf("UpdateGasPoolSponsorTx by wait gas: %v: update=%+v", terr, update)
	}
	return
}

// --- async task
func (pm *Paymaster) handleWaitGasTask(ctx context.Context, taskExpireSecs int64) {
	tasks, err := pm.asyncTaskMgr.AllWaitGasTask(ctx)
	if err != nil {
		pm.log.Errorf("AllWaitTxTask: %v", err)
		return
	}
	pm.log.Infof("handleWaitGasTask: count=%d", len(tasks))

	if len(tasks) == 0 {
		return
	}

	g := pm.waitGasTaskPool.NewGroup()

	nowUnix := time.Now().Unix()
	for _, task := range tasks {
		g.Submit(func() {
			spent := nowUnix - task.TimeUnix
			if spent >= taskExpireSecs {
				pm.log.Errorf("wait task task is expired: task=%v", task)
				err := pm.asyncTaskMgr.RemoveWaitGasTask(ctx, task.TxID)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by expired: %v: id=%d", err, task.TxID)
				}
				return
			}

			stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, task.TxID)
			if err != nil {
				pm.log.Errorf("get sponsor tx error: %v", err)
				return
			}

			if stx.Status != model.GasPoolTxStatusWaitGas {
				err := pm.asyncTaskMgr.RemoveWaitGasTask(ctx, task.TxID)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by useless tx: %v: id=%d", err, task.TxID)
				}
				return
			}

			cli := pm.tronCli.Next()
			bandwidthBalance, energyBalance, err := getAccountAvailableFee(cli, stx.From)
			if err != nil {
				pm.log.Errorf("get account available fee error, %v", err)
				return
			}
			if bandwidthBalance.LessThan(stx.Bandwidth) ||
				energyBalance.LessThan(stx.Energy) {
				return
			}
			rawTxBytes, err := hex.DecodeString(stx.RawTxHex)
			if err != nil {
				pm.log.Errorf("decode raw tx hex error: %v", err)
				return
			}
			var tx core.Transaction
			err = proto.Unmarshal(rawTxBytes, &tx)
			if err != nil {
				pm.log.Errorf("unmarshal tx raw error: %v", err)
				return
			}
			res2, err := cli.Broadcast(&tx)
			if err != nil {
				pm.log.Errorf("broadcast tx error: %v", err)
				return
			}

			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID

			// broadcast fail
			if !res2.Result {
				update.Reason = "broadcast fail:" + res2.String()
				update.Status = model.GasPoolTxStatusFail
				err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
				if err != nil {
					pm.log.Errorf("UpdateGasPoolSponsorTx by fail: %v: txID=%d, reason=%s", err, update.ID, update.Reason)
				}
				return
			}

			if stx.TxType == model.GasPoolTxTypeDeposit {
				// TODO: deposit settle
			}

			// finished
			update.Status = model.GasPoolTxStatusSuccess
			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by finished: %v: %d: %s", err, update.ID, update.Status)
			}
		})
	}
	_ = g.Wait()
}

func (pm *Paymaster) Start(ctx context.Context) error {
	if pm.stopCh == nil {
		pm.stopCh = make(chan struct{})
	}
	if pm.waitGasTaskPool == nil {
		pm.waitGasTaskPool = pond.NewPool(10)
	}
	if pm.WaitDepositTxTaskPool == nil {
		pm.WaitDepositTxTaskPool = pond.NewPool(10)
	}
	// wait gas task
	go func() {
		interval := 5 * time.Second
		var taskExpireSecs int64 = 2 * 60 // 2 minutes

		t := time.NewTimer(interval)
		defer t.Stop()

		pm.log.Info("tron paymaster async task start: wait gas task: interval=%s, taskExpireSecs=%d",
			interval.String(), taskExpireSecs)

		for {
			t.Reset(interval)
			select {
			case <-pm.stopCh:
				pm.log.Info("tron paymaster async task stop: wait gas task")
				return
			case <-t.C:
				pm.handleWaitGasTask(ctx, taskExpireSecs)
			}
		}
	}()
	return nil
}

func (pm *Paymaster) Stop(ctx context.Context) error {
	if pm.stopCh != nil {
		close(pm.stopCh)
	}
	return nil
}
