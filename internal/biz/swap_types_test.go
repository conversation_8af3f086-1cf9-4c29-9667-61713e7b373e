package biz

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestSwapToken_EVMTokenAddress(t *testing.T) {
	tests := []struct {
		name    string
		address string
		want    string
	}{
		{
			name:    "1",
			address: "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359",
			want:    "0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := SwapToken{
				TokenAddress: tt.address,
			}
			assert.Equalf(t, tt.want, s.StandardTokenAddress(), "StandardTokenAddress()")
		})
	}
}
