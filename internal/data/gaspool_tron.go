package data

import (
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"context"
	"strconv"

	"github.com/shopspring/decimal"
	tronchain "byd_wallet/internal/biz/syncer/chain/tron"
)

type tronAsyncTaskMgr struct {
	*Data
}

func NewTronAsyncTaskMgr(data *Data) tron.AsyncTaskMgr {
	return &tronAsyncTaskMgr{
		Data: data,
	}
}

func (t *tronAsyncTaskMgr) keyWaitGasTask() string {
	return "trxpm:waitgas"
}

func (t *tronAsyncTaskMgr) AddWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return t.rd.SAdd(ctx, t.keyWaitGasTask(), idstr).Err()
}

func (t *tronAsyncTaskMgr) AllWaitGasTask(ctx context.Context) ([]uint, error) {
	values, err := t.rd.SMembers(ctx, t.keyWaitGasTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}

func (t *tronAsyncTaskMgr) RemoveWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return t.rd.SRem(ctx, t.keyWaitGasTask(), idstr).Err()
}

type tronAddressChecker struct {
	*Data
	tronCli *tronchain.RoundRobinClient
}

func NewTronAddressChecker(data *Data,
	tronCli *tronchain.RoundRobinClient) tron.TronAddressChecker {
	return &tronAddressChecker{
		Data:    data,
		tronCli: tronCli,
	}
}

func (t *tronAddressChecker) IsActivatedAddress(ctx context.Context, address string) (bool, error) {
	// cli := t.tronCli.Next()
	// acc, err := cli.GetAccount(address)
	// if err != nil {
	// 	return false, err
	// }
	panic("unimplemented")
}

type tronBandwidthPayWallet struct {
}

func NewTronBandwidthPayWallet() tron.TronBandwidthPayWallet {
	return &tronBandwidthPayWallet{}
}

func (w *tronBandwidthPayWallet) TransferTrx(ctx context.Context, req *tron.TransferTrxReq) (reply *tron.TransferTrxReply, err error) {
	panic("unimplemented")
}

type tronRentApi struct {
}

func NewTronRentApi() tron.TronRentApi {
	return &tronRentApi{}
}

func (t *tronRentApi) GetEnergyPrice(ctx context.Context, req *tron.GetEnergyPriceReq) (price decimal.Decimal, err error) {
	panic("unimplemented")
}

func (t *tronRentApi) BuyEnergy(ctx context.Context, req *tron.BuyEnergyReq) (reply *tron.BuyEnergyReply, err error) {
	panic("unimplemented")
}
