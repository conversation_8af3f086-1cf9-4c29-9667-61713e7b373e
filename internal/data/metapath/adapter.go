package metapath

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/model"
	"context"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"
	"time"
)

var chainIndexToName = map[int64]string{
	constant.EthChainIndex:      ETH,
	constant.SolChainIndex:      SOLANA,
	constant.BscChainIndex:      BSC,
	constant.PolChainIndex:      POLYGON,
	constant.BaseChainIndex:     BASE,
	constant.ArbChainIndex:      ARBITRUM,
	constant.OptimismChainIndex: OPTIMISM,
	constant.TronChainIndex:     TRON,
}

var chainNameToIndex = map[string]int64{
	"ETH":      constant.EthChainIndex,
	"SOLANA":   constant.SolChainIndex,
	"BSC":      constant.BscChainIndex,
	"POLYGON":  constant.PolChainIndex,
	"BASE":     constant.BaseChainIndex,
	"ARBITRUM": constant.ArbChainIndex,
	"OPTIMISM": constant.OptimismChainIndex,
	"TRON":     constant.TronChainIndex,
}

func NewTokenSwapper(cli *Client, tronCli *tron.RoundRobinClient, logger log.Logger) biz.TokenSwapper {
	return &tokenSwapper{Client: cli, tronAdapter: newTronAdapter(tronCli), log: log.NewHelper(logger)}
}

type tokenSwapper struct {
	*Client
	tronAdapter *tronAdapter
	log         *log.Helper
}

func (t *tokenSwapper) GetOriginSwapRecordByHash(ctx context.Context, hash string) (*model.SwapRecord, error) {
	data, err := t.GetTransactionDetail(ctx, &GetTransactionDetailRequest{Hash: hash})
	if err != nil {
		return nil, err
	}
	fromChain, err := data.FromChainIndex()
	if err != nil {
		return nil, err
	}
	toChain, err := data.ToChainIndex()
	if err != nil {
		return nil, err
	}
	var details []*model.SwapDetail
	for _, tx := range data.Status {
		details = append(details, &model.SwapDetail{
			ChainIndex:  chainNameToIndex[tx.Chain],
			ExplorerURL: tx.Url,
			Hash:        tx.TxHash,
			Status:      tx.Status,
		})
	}
	return &model.SwapRecord{
		SwappedAt:        time.UnixMilli(data.CreateTimeT),
		Status:           data.LocalSwapStatus(),
		FromTokenAssetID: 0,
		FromTokenAsset: &model.TokenAsset{
			ChainIndex: fromChain,
			Address:    toLocalAddress(data.FromTokenAddress),
		},
		ToTokenAssetID: 0,
		ToTokenAsset: &model.TokenAsset{
			ChainIndex: toChain,
			Address:    toLocalAddress(data.ToTokenAddress),
		},
		FromTokenAmount: data.FromAmount.String(),
		ToTokenAmount:   data.ToTokenAmount.String(),
		FromAddress:     data.RefundAddress,
		ToAddress:       data.ReceiveAddress,
		GasFee:          "",
		FeeRate:         "",
		Hash:            data.TxHash,
		ApprovalHash:    "",
		BlockNumber:     0,
		Dex:             data.DexName,
		DexLogo:         data.LogoUrl,
		SwapPrice:       "",
		Details:         details,
		FinishedAt:      nil,
	}, nil
}

func (t *tokenSwapper) GetSwapRecord(ctx context.Context, record *model.SwapRecord) (*model.SwapRecord, error) {
	data, err := t.GetTransactionDetail(ctx, &GetTransactionDetailRequest{
		Hash: record.Hash,
	})
	if err != nil {
		return nil, err
	}
	record.Status = data.LocalSwapStatus()
	record.ToTokenAmount = toTokenValue(data.ToTokenAmount, decimal.NewFromInt(record.ToTokenAsset.Decimals)).String()
	record.Details = []*model.SwapDetail{}
	for _, tx := range data.Status {
		if tx.TxHash == "" {
			continue
		}
		record.Details = append(record.Details, &model.SwapDetail{
			SwapRecordID: record.ID,
			ChainIndex:   chainNameToIndex[tx.Chain],
			ExplorerURL:  tx.Url,
			Hash:         tx.TxHash,
			Status:       tx.Status,
		})
	}
	return record, nil
}

func (t *tokenSwapper) MultiQuote(ctx context.Context, param *biz.MultiQuoteInput) (*biz.MultiQuoteOutput, error) {
	param.From.TokenAddress = param.From.StandardTokenAddress()
	param.To.TokenAddress = param.To.StandardTokenAddress()
	if param.From.TokenAddress == "" {
		param.From.TokenAddress = NativeTokenAddress
	}
	if param.To.TokenAddress == "" {
		param.To.TokenAddress = NativeTokenAddress
	}
	routes, err := t.Client.MultiQuote(ctx, &MultiQuoteRequest{
		EquipmentNo:      GenerateEquipmentNo(param.From.Address),
		FromTokenAddress: param.From.TokenAddress,
		UserAddr:         param.From.Address,
		ToTokenAddress:   param.To.TokenAddress,
		ToAddress:        param.To.Address,
		FromTokenChain:   chainIndexToName[param.From.ChainIndex],
		ToTokenChain:     chainIndexToName[param.To.ChainIndex],
		FromTokenAmount:  param.From.Amount,
		Slippage:         param.Slippage,
		Source:           t.config.Source,
	})
	if err != nil {
		return nil, err
	}
	var quotes []*biz.QuoteInfo
	for _, route := range routes {
		quotes = append(quotes, &biz.QuoteInfo{
			Dex:                   route.Dex,
			DexLogo:               route.LogoUrl,
			FeeRate:               route.Fee,
			ReceiveTokenAmount:    toTokenValue(route.ReceiveTokenAmount, param.To.Decimals),
			MinReceiveTokenAmount: route.CalcAmountOutMin(),
			EstimatedTime:         route.EstimatedTime,
			Path:                  encodePath(route.Path),
			MaxFromTokenAmount:    toTokenValue(route.DepositMax, param.From.Decimals),
			MinFromTokenAmount:    toTokenValue(route.DepositMin, param.From.Decimals),
		})
	}

	return &biz.MultiQuoteOutput{Quotes: quotes}, nil
}

func toTokenValue(amount decimal.Decimal, decimals decimal.Decimal) decimal.Decimal {
	return amount.Mul(decimal.NewFromInt(10).Pow(decimals)).Round(0)
}

func toTokenAmount(token decimal.Decimal, decimals decimal.Decimal) decimal.Decimal {
	return token.Div(decimal.NewFromInt(10).Pow(decimals))
}

func (t *tokenSwapper) Swap(ctx context.Context, input *biz.SwapInput) (*biz.SwapOutput, error) {
	input.From.TokenAddress = input.From.StandardTokenAddress()
	input.To.TokenAddress = input.To.StandardTokenAddress()
	if input.From.TokenAddress == "" {
		input.From.TokenAddress = NativeTokenAddress
	}
	if input.To.TokenAddress == "" {
		input.To.TokenAddress = NativeTokenAddress
	}
	equipmentNo := GenerateEquipmentNo(input.From.Address)
	fromChain := chainIndexToName[input.From.ChainIndex]
	toChain := chainIndexToName[input.To.ChainIndex]
	result, err := t.Client.Swap(ctx, &SwapRequest{
		EquipmentNo:      equipmentNo,
		FromTokenAddress: input.From.TokenAddress,
		ToTokenAddress:   input.To.TokenAddress,
		FromAddress:      input.From.Address,
		ToAddress:        input.To.Address,
		AmountOutMin:     input.To.Amount,
		RouterPath:       decodePath(input.RouterPath),
		Dex:              input.Dex,
		FromTokenChain:   fromChain,
		ToTokenChain:     toChain,
		FromTokenAmount:  input.From.Amount,
		Slippage:         input.Slippage,
		Source:           t.config.Source,
	})
	if err != nil {
		return nil, err
	}
	if !result.IsValidate() {
		return nil, errors.New("invalid swap result")
	}
	transferData := result.GetTransferData()
	from := input.From.Address
	if transferData != nil {
		from = result.GetTransferData().FromAddress
	}
	switch data := result.(type) {
	case *EvmSwapTxData:
		return &biz.SwapOutput{
			From:           data.From,
			To:             data.To,
			Value:          data.Value,
			GasLimit:       data.Gas,
			GasPrice:       data.GasPrice,
			Data:           data.Data,
			ApproveAddress: data.ApproveAddress,
			ToType:         data.ToType,
			PlatformAddr:   data.PlatformAddr,
		}, nil
	case *SolanaSwapTxData:
		if data.Tx == nil {
			return nil, fmt.Errorf("no tx in SolanaSwapTxData")
		}
		programId := data.Tx[0].ProgramId
		return &biz.SwapOutput{
			From:           from,
			To:             programId,
			Data:           data.SolanaTx.SerializedMessage,
			ApproveAddress: programId,
			ToType:         ToTypeContractHaveData,
		}, nil
	case *BridgersTRONSwapTxData:
		tronTx, err := t.tronAdapter.client().TriggerContract(
			from,
			data.To,
			data.FunctionName,
			data.ParameterJSON(),
			data.Options.FeeLimit.IntPart(),
			0,
			"",
			0,
		)
		if err != nil {
			return nil, err
		}
		t.log.Debugf("metapath swapdata, dex=%s, txID=%s", input.Dex, hex.EncodeToString(tronTx.Txid))
		tronTxB, err := proto.Marshal(tronTx.Transaction.GetRawData())
		if err != nil {
			return nil, err
		}
		return &biz.SwapOutput{
			From:           from,
			To:             data.To,
			Data:           hex.EncodeToString(tronTxB),
			ApproveAddress: data.To,
			ToType:         ToTypeContractHaveData,
		}, nil
	case *AggregatorTRONSwapTxData:
		return &biz.SwapOutput{
			From:           from,
			To:             data.To,
			Value:          data.Value,
			Data:           data.Data.Transaction.RawDataHex,
			ApproveAddress: data.To,
			ToType:         ToTypeContractHaveData,
		}, nil
	case *SWFTSwapTxData:
		return &biz.SwapOutput{
			From:           from,
			To:             data.ToAddress(),
			Value:          data.Value,
			Data:           data.Data,
			ApproveAddress: data.ApproveAddress,
			ToType:         data.ToType,
			SwapPrice:      data.InstantRate,
			PlatformAddr:   data.PlatformAddr,
			OrderId:        data.OrderId,
		}, nil
	default:
		return nil, fmt.Errorf("invalid result type: %T", result)
	}

}

func (t *tokenSwapper) AddSwapRecord(ctx context.Context, input *biz.AddSwapRecordInput) (*model.SwapRecord, error) {
	input.From.TokenAddress = input.From.StandardTokenAddress()
	input.To.TokenAddress = input.To.StandardTokenAddress()
	equipmentNo := GenerateEquipmentNo(input.From.Address)
	fromChain := chainIndexToName[input.From.ChainIndex]
	toChain := chainIndexToName[input.To.ChainIndex]
	fromTokenAddress := input.From.TokenAddress
	if input.From.TokenAddress == "" {
		fromTokenAddress = NativeTokenAddress
	}
	toTokenAddress := input.To.TokenAddress
	if input.To.TokenAddress == "" {
		toTokenAddress = NativeTokenAddress
	}
	_, err := t.AddSwapInfo(ctx, &AddSwapInfoRequest{
		EquipmentNo:      equipmentNo,
		FromTokenAddress: fromTokenAddress,
		ToTokenAddress:   toTokenAddress,
		FromAddress:      input.From.Address,
		ToAddress:        input.To.Address,
		FromTokenAmount:  input.From.Amount,
		Slippage:         input.Slippage,
		FromChain:        fromChain,
		ToChain:          toChain,
		Hash:             input.Hash,
		ToTokenAmount:    toTokenAmount(input.To.Amount, input.To.Decimals),
		DexName:          input.Dex,
		Source:           t.config.Source,
		IsNoGas:          "1",
		OrderId:          input.OrderId,
	})
	if err != nil {
		return nil, err
	}
	txDetail, err := t.GetTransactionDetail(ctx, &GetTransactionDetailRequest{
		Hash: input.Hash,
	})
	if err != nil {
		return nil, err
	}
	var details []*model.SwapDetail
	for _, tx := range txDetail.Status {
		if tx.TxHash == "" {
			continue
		}
		details = append(details, &model.SwapDetail{
			ChainIndex:  chainNameToIndex[tx.Chain],
			ExplorerURL: tx.Url,
			Hash:        tx.TxHash,
			Status:      tx.Status,
		})
	}
	return &model.SwapRecord{
		SwappedAt:        time.UnixMilli(txDetail.CreateTimeT),
		Status:           txDetail.LocalSwapStatus(),
		FromTokenAssetID: input.From.TokenAssetID,
		ToTokenAssetID:   input.To.TokenAssetID,
		FromTokenAmount:  input.From.Amount.String(),
		ToTokenAmount:    input.To.Amount.String(),
		FromAddress:      input.From.Address,
		ToAddress:        input.To.Address,
		FeeRate:          input.FeeRate.String(),
		Hash:             input.Hash,
		ApprovalHash:     input.ApprovalHash,
		BlockNumber:      0,
		Dex:              input.Dex,
		DexLogo:          txDetail.LogoUrl,
		SwapPrice:        input.SwapPrice,
		Details:          details,
		FinishedAt:       nil,
	}, nil
}

func NewSwapTokenFetcher(cli *Client, logger log.Logger) biz.SwapTokenFetcher {
	return &swapTokenFetcher{Client: cli, log: log.NewHelper(logger)}
}

type swapTokenFetcher struct {
	*Client
	log *log.Helper
}

func (s *swapTokenFetcher) FetchSwapTokens(ctx context.Context) ([]*model.TokenAsset, error) {
	data, err := s.ListAllToken(ctx)
	if err != nil {
		return nil, err
	}
	var output []*model.TokenAsset
	for chain, tokens := range data.Tokens {
		for _, token := range tokens {
			chainIndex, ok := chainNameToIndex[chain]
			if !ok {
				//s.log.Warnf("swapTokenFetcher: chain(%s) not exist", chain)
				continue
			}
			output = append(output, &model.TokenAsset{
				ChainIndex: chainIndex,
				Address:    toLocalAddress(token.Address),
				LogoUrl:    token.LogoURI,
				Symbol:     token.Symbol,
				Name:       token.Name,
				Decimals:   int64(token.Decimals),
			})
		}
	}
	return output, nil
}

func toLocalAddress(address string) string {
	if address == NativeTokenAddress {
		return ""
	}
	return address
}
