package metapath

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/stretchr/testify/require"
	"sync"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func newTestSwapper(t *testing.T) biz.TokenSwapper {
	logger := log.DefaultLogger
	config := &Config{
		BaseURL: "https://api-swap.paths.finance",
		Source:  "bosswallet",
		Debug:   true,
		// ProxyURL 为空，不使用代理
	}
	client := NewClient(config, logger)
	tronCli, err := tron.NewRoundRobinClient([]string{"https://cool-proportionate-wind.tron-mainnet.quiknode.pro/cea0ad09c07a84b7071806a79651deafba758175/jsonrpc"})
	assert.NoError(t, err)
	return NewTokenSwapper(client, tronCli, logger)
}

func TestTokenSwapper_AddSwapRecord(t *testing.T) {
	s := newTestSwapper(t)
	record, err := s.AddSwapRecord(context.Background(), &biz.AddSwapRecordInput{
		From: &biz.SwapToken{
			ChainIndex:   constant.EthChainIndex,
			TokenAddress: "",
			Address:      "******************************************",
			Amount:       decimal.NewFromFloat(2381410000000000),
			Decimals:     decimal.NewFromFloat(6),
		},
		To: &biz.SwapToken{
			ChainIndex:   constant.EthChainIndex,
			TokenAddress: "",
			Address:      "******************************************",
			Amount:       decimal.NewFromFloat(85270220),
			Decimals:     decimal.NewFromFloat(6),
		},
		Slippage:      decimal.Decimal{},
		RouterPath:    "",
		Dex:           "Bridgers",
		Hash:          "0x082c5778ad2a3130bcd5d4ce0e4a5db7c268c77f6ccea1d4f40df9e92773f0b0",
		ApprovalHash:  "",
		SwapPrice:     "",
		FeeRate:       decimal.Decimal{},
		EstimatedTime: "",
		GasFee:        "",
	})
	assert.NoError(t, err)
	fmt.Println(record.ToTokenAmount)
}

func TestTokenSwapper_MultiQuote_Swap(t *testing.T) {
	testSuites := []struct {
		name       string
		quoteInput *biz.MultiQuoteInput
	}{
		{
			name: "SOL",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.SolChainIndex,
					TokenAddress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
					Address:      "FkdDHrM8j8psKbxuwjV1jBKCM2JGPygkj7WCX8sSCzNm",
					Amount:       decimal.NewFromInt(100000000),
					Decimals:     decimal.NewFromInt(6),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.SolChainIndex,
					TokenAddress: "",
					Address:      "FkdDHrM8j8psKbxuwjV1jBKCM2JGPygkj7WCX8sSCzNm",
					Decimals:     decimal.NewFromInt(9),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "Br_TRON",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Amount:       decimal.NewFromInt(100000000),
					Decimals:     decimal.NewFromInt(6),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "Agg_TRON",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.NewFromInt(3000000),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "EVM",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
					Amount:       decimal.NewFromFloat(1e16),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "******************************************",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "EVM_CrossChain",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.PolChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
					Amount:       decimal.NewFromFloat(100e18),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "******************************************",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "Evm_Sol",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
					Amount:       decimal.NewFromFloat(1e16),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.SolChainIndex,
					TokenAddress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
					Address:      "FkdDHrM8j8psKbxuwjV1jBKCM2JGPygkj7WCX8sSCzNm",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "sol_evm",
			quoteInput: &biz.MultiQuoteInput{
				To: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
				},
				From: &biz.SwapToken{
					ChainIndex:   constant.SolChainIndex,
					TokenAddress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
					Address:      "FkdDHrM8j8psKbxuwjV1jBKCM2JGPygkj7WCX8sSCzNm",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.NewFromFloat(10e18),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "Evm_tron",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
					Amount:       decimal.NewFromFloat(1e16),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "tron_evm",
			quoteInput: &biz.MultiQuoteInput{
				To: &biz.SwapToken{
					ChainIndex:   constant.EthChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
				},
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.NewFromFloat(1e7),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "tron_sol",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.NewFromFloat(1e8),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.SolChainIndex,
					TokenAddress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
					Address:      "FkdDHrM8j8psKbxuwjV1jBKCM2JGPygkj7WCX8sSCzNm",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "sol_tron",
			quoteInput: &biz.MultiQuoteInput{
				To: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
				},
				From: &biz.SwapToken{
					ChainIndex:   constant.SolChainIndex,
					TokenAddress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
					Address:      "FkdDHrM8j8psKbxuwjV1jBKCM2JGPygkj7WCX8sSCzNm",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.NewFromFloat(1e8),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "bnb_usdt->arb-eth",
			quoteInput: &biz.MultiQuoteInput{
				To: &biz.SwapToken{
					ChainIndex:   constant.BscChainIndex,
					TokenAddress: "******************************************",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
					Amount:       decimal.NewFromFloat(1409713000000000000),
				},
				From: &biz.SwapToken{
					ChainIndex: constant.ArbChainIndex,
					Address:    "******************************************",
					Decimals:   decimal.NewFromInt(18),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "trx-usdt",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.NewFromFloat(1e7),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TSNsbECoPGu4N2kEqpSCqYzXJsvTyHe5aC",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "tron-bsc",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.RequireFromString("325992666"),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.BscChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "bsc-pol",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.BscChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
					Amount:       decimal.RequireFromString("25700300000000000"),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.PolChainIndex,
					TokenAddress: "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "base-pol",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.BaseChainIndex,
					TokenAddress: "0x833589fcd6edb6e08f4c7c32d4f71b54bda02913",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.RequireFromString("25700300000000000"),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.PolChainIndex,
					TokenAddress: "0x3c499c542cef5e3811e1192ce70d8cc03d5c3359",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(6),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
		{
			name: "tron_usdt->arb",
			quoteInput: &biz.MultiQuoteInput{
				From: &biz.SwapToken{
					ChainIndex:   constant.TronChainIndex,
					TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
					Address:      "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY",
					Decimals:     decimal.NewFromInt(6),
					Amount:       decimal.RequireFromString("15000000"),
				},
				To: &biz.SwapToken{
					ChainIndex:   constant.ArbChainIndex,
					TokenAddress: "",
					Address:      "******************************************",
					Decimals:     decimal.NewFromInt(18),
				},
				Slippage: decimal.NewFromFloat(1),
			},
		},
	}
	ts := newTestSwapper(t)
	for _, s := range testSuites {
		t.Run(s.name, func(t *testing.T) {
			ctx := context.Background()
			quoteInput := s.quoteInput
			from := *quoteInput.From
			to := *quoteInput.To
			quoteOutput, err := ts.MultiQuote(ctx, quoteInput)
			assert.NoError(t, err)
			require.True(t, len(quoteOutput.Quotes) > 0)
			for _, quote := range quoteOutput.Quotes {
				t.Log("dex", quote.Dex)
				quoteInput.To.Amount = quote.MinReceiveTokenAmount /*.Mul(decimal.NewFromFloat(0.9)).Truncate(0)*/
				swapFrom := from.Copy()
				swapTo := to.Copy()
				swapOutput, err := ts.Swap(ctx, &biz.SwapInput{
					From:       &swapFrom,
					To:         &swapTo,
					Slippage:   quoteInput.Slippage,
					RouterPath: quote.Path,
					Dex:        quote.Dex,
				})
				assert.NoError(t, err)
				b, err := json.Marshal(swapOutput)
				assert.NoError(t, err)
				fmt.Println(string(b))
			}
		})
	}
}

func TestTokenSwapper_Swap(t *testing.T) {
	ts := newTestSwapper(t)
	ctx := context.Background()
	swapOutput, err := ts.Swap(ctx, &biz.SwapInput{
		From: &biz.SwapToken{
			TokenAssetID: 0,
			ChainIndex:   constant.TronChainIndex,
			TokenAddress: "",
			Address:      "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY",
			Amount:       decimal.RequireFromString("20000000"),
			Decimals:     decimal.NewFromInt(6),
		},
		To: &biz.SwapToken{
			TokenAssetID: 0,
			ChainIndex:   constant.TronChainIndex,
			TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
			Address:      "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY",
			Amount:       decimal.RequireFromString("5037196"),
			Decimals:     decimal.NewFromInt(6),
		},
		Slippage:   decimal.NewFromInt(1),
		RouterPath: ``,
		Dex:        "SWFT",
	})
	assert.NoError(t, err)
	b, err := json.Marshal(swapOutput)
	assert.NoError(t, err)
	fmt.Println(string(b))
}

func TestTronAddress(t *testing.T) {
	from := address.HexToAddress("417af1e1058f1e2b3db4a2a905b9095ebe0d2928b6").String()
	fromDesc, err := address.Base58ToAddress(from)
	assert.NoError(t, err)
	fmt.Println(fromDesc)
}

func TestTokenSwapper_GetSwapRecord(t *testing.T) {
	ctx := context.Background()
	ts := newTestSwapper(t)
	record, err := ts.GetSwapRecord(ctx, &model.SwapRecord{
		Hash: "0xb8a1a12e57fc87bbce96706c72fde97f2043e1039bcf584e6615890e2c4b7d46",
		Details: []*model.SwapDetail{
			{
				SwapRecordID: 0,
				ExplorerURL:  "",
				Hash:         "0xb8a1a12e57fc87bbce96706c72fde97f2043e1039bcf584e6615890e2c4b7d46",
				Status:       "",
				Collected:    false,
			},
		},
	})
	assert.NoError(t, err)
	b, _ := json.Marshal(record)
	fmt.Println(string(b))
}

func Test_toTokenValue(t *testing.T) {
	type args struct {
		amount   decimal.Decimal
		decimals decimal.Decimal
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "1",
			args: args{
				amount:   decimal.NewFromFloat(0.000001),
				decimals: decimal.NewFromInt(6),
			},
			want: "1",
		},
		{
			name: "2",
			args: args{
				amount:   decimal.NewFromFloat(0.000011),
				decimals: decimal.NewFromInt(6),
			},
			want: "11",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, toTokenValue(tt.args.amount, tt.args.decimals).String(), "toTokenValue(%v, %v)", tt.args.amount, tt.args.decimals)
		})
	}
}

// TestTokenSwapper_MultiQuote 对比测试有无代理的成功率和请求速度
func TestTokenSwapper_MultiQuoteConcurrency(t *testing.T) {
	ctx := context.Background()

	// 测试参数
	testInput := &biz.MultiQuoteInput{
		From: &biz.SwapToken{
			TokenAssetID: 0,
			ChainIndex:   20000714,
			TokenAddress: "",
			Address:      "******************************************",
			Amount:       decimal.RequireFromString("34376920000000000"),
			Decimals:     decimal.NewFromInt(18),
		},
		To: &biz.SwapToken{
			TokenAssetID: 0,
			ChainIndex:   195,
			TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
			Address:      "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY",
			Decimals:     decimal.NewFromInt(6),
		},
		Slippage: decimal.NewFromInt(9),
	}

	// 测试次数
	testCount := 5

	// 测试无代理配置
	t.Run("Without Proxy", func(t *testing.T) {
		ts := newTestSwapper(t)
		var successCount int
		var totalDuration time.Duration
		var errors []error

		for i := 0; i < testCount; i++ {
			start := time.Now()
			_, err := ts.MultiQuote(ctx, testInput)
			duration := time.Since(start)
			totalDuration += duration

			if err != nil {
				errors = append(errors, err)
				t.Logf("无代理请求 %d 失败: %v (耗时: %v)", i+1, err, duration)
			} else {
				successCount++
				t.Logf("无代理请求 %d 成功 (耗时: %v)", i+1, duration)
			}
		}

		successRate := float64(successCount) / float64(testCount) * 100
		avgDuration := totalDuration / time.Duration(testCount)

		t.Logf("=== 无代理测试结果 ===")
		t.Logf("总请求次数: %d", testCount)
		t.Logf("成功次数: %d", successCount)
		t.Logf("失败次数: %d", len(errors))
		t.Logf("成功率: %.2f%%", successRate)
		t.Logf("平均响应时间: %v", avgDuration)
		t.Logf("总耗时: %v", totalDuration)

		// 至少要有一次成功
		assert.True(t, successCount > 0, "无代理测试应该至少有一次成功")
	})

	// 测试有代理配置
	t.Run("With Proxy", func(t *testing.T) {
		ts := newTestSwapper(t)

		var successCount int
		var totalDuration time.Duration
		var errors []error

		for i := 0; i < testCount; i++ {
			start := time.Now()
			_, err := ts.MultiQuote(ctx, testInput)
			duration := time.Since(start)
			totalDuration += duration

			if err != nil {
				errors = append(errors, err)
				t.Logf("有代理请求 %d 失败: %v (耗时: %v)", i+1, err, duration)
			} else {
				successCount++
				t.Logf("有代理请求 %d 成功 (耗时: %v)", i+1, duration)
			}
		}

		successRate := float64(successCount) / float64(testCount) * 100
		avgDuration := totalDuration / time.Duration(testCount)

		t.Logf("=== 有代理测试结果 ===")
		t.Logf("总请求次数: %d", testCount)
		t.Logf("成功次数: %d", successCount)
		t.Logf("失败次数: %d", len(errors))
		t.Logf("成功率: %.2f%%", successRate)
		t.Logf("平均响应时间: %v", avgDuration)
		t.Logf("总耗时: %v", totalDuration)

		// 至少要有一次成功
		assert.True(t, successCount > 0, "有代理测试应该至少有一次成功")
	})
}

// TestTokenSwapper_MultiQuote_Comparison 对比测试结果（支持并发和超时控制）
func TestTokenSwapper_MultiQuote_Comparison(t *testing.T) {
	ctx := context.Background()

	// 测试参数
	testInput := &biz.MultiQuoteInput{
		From: &biz.SwapToken{
			TokenAssetID: 0,
			ChainIndex:   20000714,
			TokenAddress: "",
			Address:      "******************************************",
			Amount:       decimal.RequireFromString("34376920000000000"),
			Decimals:     decimal.NewFromInt(18),
		},
		To: &biz.SwapToken{
			TokenAssetID: 0,
			ChainIndex:   195,
			TokenAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
			Address:      "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY",
			Decimals:     decimal.NewFromInt(6),
		},
		Slippage: decimal.NewFromInt(9),
	}

	testCount := 100                   // 测试次数
	concurrency := 5                   // 并发数
	requestTimeout := 15 * time.Second // 每个请求的超时时间

	// 测试函数（支持并发和超时控制）
	testConfig := func(name string, proxyURL string) (successRate float64, avgDuration time.Duration) {
		ts := newTestSwapper(t)

		// 用于收集结果的结构体
		type result struct {
			success  bool
			duration time.Duration
			err      error
		}

		results := make(chan result, testCount)
		semaphore := make(chan struct{}, concurrency) // 控制并发数
		var wg sync.WaitGroup

		// 启动并发请求
		for i := 0; i < testCount; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				// 获取信号量，控制并发数
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				// 为每个请求创建带超时的 context
				requestCtx, cancel := context.WithTimeout(ctx, requestTimeout)
				defer cancel()

				start := time.Now()
				_, err := ts.MultiQuote(requestCtx, testInput)
				duration := time.Since(start)

				results <- result{
					success:  err == nil,
					duration: duration,
					err:      err,
				}

				if err != nil {
					t.Logf("%s 请求 %d 失败: %v (耗时: %v)", name, index+1, err, duration)
				} else {
					t.Logf("%s 请求 %d 成功 (耗时: %v)", name, index+1, duration)
				}
			}(i)
		}

		// 等待所有请求完成
		wg.Wait()
		close(results)

		// 统计结果
		var successCount int
		var totalDuration time.Duration
		var timeoutCount int

		for result := range results {
			if result.success {
				successCount++
			}
			totalDuration += result.duration

			// 检查是否是超时错误
			if result.err != nil && errors.Is(result.err, context.DeadlineExceeded) {
				timeoutCount++
			}
		}

		successRate = float64(successCount) / float64(testCount) * 100
		avgDuration = totalDuration / time.Duration(testCount)

		t.Logf("%s 统计: 成功 %d/%d, 超时 %d, 成功率 %.2f%%, 平均耗时 %v",
			name, successCount, testCount, timeoutCount, successRate, avgDuration)

		return
	}

	// 测试无代理
	noProxySuccessRate, noProxyAvgDuration := testConfig("无代理", "")

	// 测试有代理
	proxySuccessRate, proxyAvgDuration := testConfig("有代理", "http://127.0.0.1:7890")

	// 输出对比结果
	t.Logf("\n=== 代理对比测试结果（并发数: %d, 超时: %v）===", concurrency, requestTimeout)
	t.Logf("测试次数: %d", testCount)
	t.Logf("")
	t.Logf("无代理:")
	t.Logf("  成功率: %.2f%%", noProxySuccessRate)
	t.Logf("  平均响应时间: %v", noProxyAvgDuration)
	t.Logf("")
	t.Logf("有代理:")
	t.Logf("  成功率: %.2f%%", proxySuccessRate)
	t.Logf("  平均响应时间: %v", proxyAvgDuration)
	t.Logf("")
	t.Logf("对比分析:")
	t.Logf("  成功率差异: %.2f%% (代理 - 直连)", proxySuccessRate-noProxySuccessRate)
	if proxyAvgDuration > noProxyAvgDuration {
		t.Logf("  响应时间: 代理比直连慢 %v", proxyAvgDuration-noProxyAvgDuration)
	} else {
		t.Logf("  响应时间: 代理比直连快 %v", noProxyAvgDuration-proxyAvgDuration)
	}

	// 基本断言
	assert.True(t, noProxySuccessRate > 0 || proxySuccessRate > 0, "至少有一种配置应该能成功")
}

func Test_toTokenAmount(t *testing.T) {
	type args struct {
		token    decimal.Decimal
		decimals decimal.Decimal
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "1",
			args: args{
				token:    decimal.NewFromFloat(1110000000),
				decimals: decimal.NewFromInt(6),
			},
			want: "1110",
		},
		{
			name: "2",
			args: args{
				token:    decimal.NewFromFloat(10000),
				decimals: decimal.NewFromInt(7),
			},
			want: "0.001",
		},
		{
			name: "3",
			args: args{
				token:    decimal.NewFromFloat(0),
				decimals: decimal.NewFromInt(7),
			},
			want: "0",
		},
		{
			name: "4",
			args: args{
				token:    decimal.NewFromFloat(8527022000000000000),
				decimals: decimal.NewFromInt(18),
			},
			want: "8.527022",
		},
		{
			name: "5",
			args: args{
				token:    decimal.NewFromFloat(100),
				decimals: decimal.Decimal{},
			},
			want: "100",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, toTokenAmount(tt.args.token, tt.args.decimals).String(), "toTokenAmount(%v, %v)", tt.args.token, tt.args.decimals)
		})
	}
}
