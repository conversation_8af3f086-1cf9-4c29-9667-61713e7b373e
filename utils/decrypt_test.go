package utils

import (
	"fmt"
	"testing"
)

func TestEncryptWithAESGCM(t *testing.T) {
	keyStr, _ := generateAESKeyBase64(32) // 256-bit key
	fmt.Println("🔐 keyStr:", keyStr)
	key, _ := decodeAESKeyBase64(keyStr)
	cipherText, _ := EncryptWithAESGCM([]byte("43f0de8fba023ad5f55eb626f2cb2507bc0cd33ed7598a46cff380684a2c48b7"), key)
	plainText, _ := DecryptWithAESGCM(cipherText, keyStr)

	fmt.Println("🔐 keyStr:", keyStr)
	fmt.Println("🔐 Encrypted:", cipherText)
	fmt.Println("🔓 Decrypted:", string(plainText))
}
